{"pages": ["pages/home/<USER>", "pages/login/login", "pages/errPage/errPage", "pages/business/business", "pages/companyInfo/companyInfo", "pages/companyInfo/infoadd/infoadd", "pages/report/report", "pages/mine/mine", "pages/card/card", "pages/login/agreement/index", "pages/hIndusty/index"], "requiredPrivateInfos": ["getLocation", "startLocationUpdate", "onLocationChange"], "tabBar": {"backgroundColor": "#ffffff", "color": "#20263A", "selectedColor": "#E72410", "borderStyle": "white", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "image/tabbar/home.png", "selectedIconPath": "image/tabbar/home_a.png", "text": "首页"}, {"pagePath": "pages/business/business", "iconPath": "image/tabbar/recommend.png", "selectedIconPath": "image/tabbar/recommend_a.png", "text": "项目"}, {"pagePath": "pages/hIndusty/index", "iconPath": "image/tabbar/industry.png", "selectedIconPath": "image/tabbar/industry_a.png", "text": "产业"}, {"pagePath": "pages/report/report", "iconPath": "image/tabbar/report.png", "selectedIconPath": "image/tabbar/report_a.png", "text": "报告"}, {"pagePath": "pages/mine/mine", "iconPath": "image/tabbar/mine.png", "selectedIconPath": "image/tabbar/mine_a.png", "text": "我的"}]}, "subpackages": [{"root": "companyPackage/", "name": "companyPackage", "pages": ["pages/projectManage/index", "pages/mine/minesign/minesign", "pages/mine/mineSuggest/index", "pages/mine/suggestResult/index", "pages/mine/mineEditPreset/index", "pages/mine/mineFeedback/index", "pages/mine/mineInvoice/index", "pages/mine/mineNews/index", "pages/mine/minePreset/index", "pages/mine/invoiceAdd/index", "pages/searchs/searchs", "pages/searchsBoss/searchs", "pages/industry/industry", "pages/intelligent/intelligent", "pages/merchantsMap/merchants", "pages/industryChain/chainSearch/chainSearch", "pages/industryChain/chainListNew/chainList", "pages/businessAdd/businessAdd", "pages/publish/pubFollow/index", "pages/publish/pubPlan/index", "pages/publish/pubAdduser/index", "pages/publish/pubAdd/index", "pages/mineRelation/relation", "pages/cardDetail/cardDetail", "pages/addCard/addCard", "pages/addMyCard/addMyCard", "pages/companyInfo/companyInfo", "pages/mineRelation/search/index", "pages/searTerm/sear-term", "pages/companyInfo/infoadd/infoadd", "pages/industryChain/newChainMap/newChainMap", "pages/businessCardList/businessCardList", "pages/bindCardList/bindCardList", "pages/searchBusiness/searchBusiness", "pages/authoritativeList/authoritativeList", "pages/authoritativeDetail/authoritativeDetail", "pages/mine/mineinvite/mineinvite", "pages/mine/mineExport/mineExport", "pages/mine/issueAnInvoice/index", "pages/mine/invoiceInfo/index", "pages/mine/invoiceOk/index", "pages/mine/setting/index", "pages/accountSetting/index", "pages/reviseName/index", "pages/conceal/index", "pages/userAgreement/index", "pages/accountCancellation/index", "pages/revisePwd/index", "pages/accountCancelNote/index", "pages/accountCancelPhone/index", "pages/imageCropper/index", "pages/projectDetail/index", "pages/logoutSuccess/index", "pages/projectAdd/index", "pages/projectManage/funPage/pubAdduser/index", "pages/projectManage/funPage/pubFollow/index", "pages/projectManage/funPage/pubPlan/index", "pages/merchantsMap/component/adressSearch/index", "pages/authoritativeEnter/index", "pages/mapSearchList/index"]}, {"root": "subPackage/", "name": "subPackage", "pages": ["pages/searchPark/index", "pages/articledetails/articledetails", "pages/pubDetails/pubdetails", "pages/collectDetails/collectdetail", "pages/science/scienceEnter/index", "pages/science/scienceDetail/index", "pages/financing/home/<USER>", "pages/financing/detail/index", "pages/financing/components/sear/index", "pages/webs/index", "pages/webDraw/index", "pages/informationDetail/index", "pages/policyDetail/policy", "pages/polishingDetail/polishing", "pages/capitaDetails/index", "pages/resourceDetails/index", "pages/carrierDetail/index", "pages/organization/index", "pages/payDetail/paydetail", "pages/orderDetail/orderdetail", "pages/dailyDetail/dailyDetail", "pages/organization/orgEvents/index", "pages/organization/information/index", "pages/searchPark/detail/index", "pages/searchPark/parkEntList/index", "pages/publish/publish"]}, {"root": "chartSubpackage/", "name": "chartSubpackage", "pages": ["pages/relationdetails/index", "pages/relationAtlas/index", "pages/orgDetail/index"]}, {"root": "childSubpackage/", "name": "childSubpackage", "pages": ["pages/monitoreDetails/index", "pages/dailypaperDetail/index", "pages/fullApplication/full", "pages/updateDynamics/index", "pages/updaetDyDetail/index", "pages/operation/index", "pages/operationDetail/index", "pages/huntList/searchs", "pages/memberManagement/index", "pages/projectManagement/index", "pages/memberManagement/chechUser/index", "pages/agreement/index", "pages/ResearchReportList/index", "pages/ReReportList/index", "pages/thinkTankList/index", "pages/ResearchThreeList/index"]}, {"root": "industryPackage/", "name": "industryPackage", "pages": ["pages/chainSearch/index", "pages/allChainList/index", "pages/IndustryListVip/index", "pages/businessList/index", "pages/businessMapList/index", "pages/IndustryListMasonry/index", "pages/IndustryMapMasonry/index", "pages/innovation/index", "pages/regionalComparison/index", "pages/listedCompany/index", "pages/comparisonDetail/index"]}], "preloadRule": {"pages/home/<USER>": {"network": "all", "packages": ["__APP__"]}}, "permission": {"scope.userLocation": {"desc": "拒绝将无法获取信息"}}, "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#E71A0F", "navigationBarTitleText": "招商", "enablePullDownRefresh": false, "backgroundColor": "#fff"}, "style": "v2", "sitemapLocation": "sitemap.json", "usingComponents": {"van-popup": "@vant/weapp/popup/index", "refresh-scroll": "components/refresh-scroll/index"}}