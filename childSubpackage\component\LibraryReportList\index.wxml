<!-- 撼地智库完整页面组件 -->
<view class="library-report-list">
  <!-- 列表页面 -->
  <view class="list-container">
    <view class="company_num"> </view>

    <!-- VIP用户：正常显示列表 -->
    <view wx:if="{{isVip}}" class="vip-container">
      <!-- 如果有数据，直接显示列表 -->
      <view wx:if="{{reportList.length > 0}}" class="direct-list">
        <ReportCard
          report-list="{{reportList}}"
          bindreportclick="onReportClick"
        />
      </view>

      <!-- 如果没有数据，使用 refresh-scroll 组件 -->
      <view wx:else class="card-box" style="height: {{containerHeight}}px;">
        <refresh-scroll
          id="libraryRefreshScroll"
          container-height="{{containerHeight}}"
          request-url="{{RequestUrlFn}}"
          requestParams="{{requestParams}}"
          empty-text="暂无研报数据"
          empty-tip=""
          bind:datachange="onDataChange"
          bind:error="onError"
          custom-wrapper-class="custom-wrapper-class"
          requestType="pageIndex"
        >
          <view slot="content" class="list_wrp">
            <ReportCard
              report-list="{{reportList}}"
              bindreportclick="onReportClick"
            />
          </view>
        </refresh-scroll>
      </view>
    </view>

    <!-- 非VIP用户：显示一条数据 + VIP页面 -->
    <scroll-view
      wx:else
      class="non-vip-container"
      scroll-y="{{true}}"
      style="height: {{containerHeight}}px;"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <!-- 显示第一条研报数据 -->
      <view wx:if="{{firstReportData}}" class="first-report-container">
        <SingleReportCard
          report-data="{{firstReportData}}"
          bindreportclick="onReportClick"
        />
      </view>

      <!-- VIP购买页面 -->
      <vip-page wx:if="{{showVipPage}}" bind:paySuccess="onVipPaySuccess">
        <!-- 可以在这里添加额外的提示内容 -->
      </vip-page>
    </scroll-view>
  </view>
</view>
