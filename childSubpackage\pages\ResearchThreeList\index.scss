@import "../../../template/more/more.scss";
@import "../../../template/null/null.scss";
@import "../../../template/loading/index.scss";

/* input */
.pages {
  height: 100vh;
  overflow: hidden;
}

/* 确保 hidden 元素不占用空间 */
[hidden] {
  display: none !important;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* input */
.searchs {
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 28rpx 24rpx;
  background: #eeeeee;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 28rpx;
}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.s-input-img {
  width: 40rpx;
  height: 40rpx;
}

input {
  caret-color: #e72410;
  color: #74798c;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
}

.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

/* 最近搜索和浏览历史样式  */
.history_wrap {
  background-color: #f7f7f7;
  padding-top: 20rpx;
}
.page__autofit {
  background-color: #fff;
  padding: 28rpx 0;
}
.page__autofit:nth-child(1) {
  margin-top: 0;
}
.search_b {
  padding-bottom: 0 !important;
}

.his_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx;
  border-bottom: 1px solid #eeeeee;
}

.his_titles {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx;
  border-bottom: 1px solid #eeeeee;
}

.his_title_l {
  font-size: 32rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.his_title_icon {
  width: 40rpx;
  height: 40rpx;
}

.his_content {
  display: flex;
  width: 100%;
  overflow: hidden;
  /* border: 1px solid red; */
  /* height: 168rpx; */
  max-height: 160rpx;
  padding: 0 32rpx;
}

.text-box {
  text-align: justify;
  display: flex;
  flex-wrap: wrap;
  /* text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical; */
}

.his_content_item {
  /* display: inline-flex; */
  background: #f5f6f7;
  border-radius: 8rpx;
  margin-top: 20rpx;
  margin-right: 20rpx;
  padding: 8rpx 20rpx;
  min-width: 96rpx;
  max-width: 680rpx;
  height: 56rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  justify-content: center;
  text-align: center;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
}

/* 暂无历史搜索 */
.his_content_none {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 104rpx;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.his_content1_item {
  height: 80rpx;
}

/* 筛选组件 */

/* 缺省页 */
.queshen {
  width: 100%;
}

/* 卡片  */
.card-box {
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card-box-text {
  width: 100%;
  height: 120rpx;
  line-height: 120rpx;
  text-align: center;
  background: #fff;
  border: 1px;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
  border-top: 1px dashed #eeeeee;
}

.his_content1_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  height: auto;
}

.his_content1_item-l {
  flex: 1;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.his_content1_item-r {
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #9b9eac;
  min-width: 80rpx;
  flex-shrink: 0;
}

/* 搜索结果样式 */
.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.company_num {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #f7f7f7;

  .text_left {
    font-size: 28rpx;
    font-weight: 400;
    color: #74798c;

    .color_num {
      color: #e72410;
      font-weight: 600;
    }
  }
}

// 更多加载
.custom-wrapper-class {
  width: 100vw;
  .list_wrp {
    width: 100vw;
  }
  .initial-loading-container {
    width: 100vw;
  }
  .refresh-scroll-container {
    width: 100vw;
  }
}

// AI图片样式
.ai_img {
  width: 750rpx;
  height: 172rpx;
  margin: 20rpx 0;
}

// 浏览历史样式
.search_b {
  padding-bottom: 0rpx !important;
}

.his_content1 {
  padding: 0 32rpx 20rpx;

  &_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    height: auto;

    &-l {
      flex: 1;
      font-size: 28rpx;
      font-family:
        PingFang SC,
        PingFang SC-Regular;
      font-weight: 400;
      text-align: LEFT;
      color: #20263a;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    &-r {
      font-size: 28rpx;
      font-family:
        PingFang SC,
        PingFang SC-Regular;
      font-weight: 400;
      text-align: RIGHT;
      color: #9b9eac;
    }
  }
}

// 搜索结果内容样式
.search-content {
  padding: 20rpx 32rpx;
  background-color: #fff;

  .search-header {
    padding: 20rpx 0;
    border-bottom: 1px solid #eeeeee;

    .search-keyword {
      font-size: 32rpx;
      font-weight: 500;
      color: #20263a;
      display: block;
      margin-bottom: 8rpx;
    }

    .search-count {
      font-size: 26rpx;
      color: #74798c;
    }
  }

  .search-results {
    padding-top: 20rpx;

    .result-item {
      padding: 20rpx 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .result-title {
        display: block;
        font-size: 30rpx;
        font-weight: 500;
        color: #20263a;
        margin-bottom: 10rpx;
      }

      .result-desc {
        display: block;
        font-size: 26rpx;
        color: #74798c;
        line-height: 1.5;
        margin-bottom: 8rpx;
      }

      .result-type {
        font-size: 24rpx;
        color: #e72410;
        background-color: #fff2f0;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        display: inline-block;
      }
    }
  }
}
