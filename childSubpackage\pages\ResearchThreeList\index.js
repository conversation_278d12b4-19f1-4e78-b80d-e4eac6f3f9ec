import {getHeight} from '../../../utils/height';
import {debounce} from '../../../utils/formate';
import {
  getReportPageListApi,
  addHistory,
  getHistory,
  clearHistory,
  addBevHis,
  detBevHis,
  getBevHis
} from '../../../service/industryApi';

const app = getApp();

Page({
  data: {
    inputShowed: true,
    // 搜索相关
    report_name: '', //搜索值
    historyList: [],
    browsingHistory: [], //浏览历史 - 必须保留
    scrollHeight: 600, // 列表滚动高度，设置默认值
    searchScrollHeight: 600, // 搜索结果滚动高度，设置默认值
    isLogin: app.isLogin(),
    // 研报数据
    searchReportList: [],
    searchRequestParams: {
      //   classic_industry_id: '2495114022756352',
      keyword: '', // 搜索关键词
      type: 'chainMap' // 热门研报
      //   type: 'classic' // 热门研报
    },
    RequestUrlFn: getReportPageListApi,
    // 搜索结果数据
    company_num: 0,
    // 高度计算相关
    isHeightCalculated: false // 是否已计算过高度
  },

  onLoad(options) {
    // 获取传递的参数
    const {type} = options;
    if (type) {
      // 根据type参数设置不同的搜索类型
      this.setData({
        'searchRequestParams.type': type
      });
    }
  },

  onShow() {
    const {login} = app.globalData;
    this.setData(
      {
        isLogin: login
      },
      () => {
        login &&
          Promise.all([this.getSearchHisList(), this.getBevHisList()]).finally(
            () => {
              wx.hideLoading();
            }
          );
      }
    );
  },

  onReady() {
    // 页面渲染完成后一次性计算所有高度
    setTimeout(() => {
      this.calculateAllHeights();
    }, 100);
  },

  onUnload() {
    wx.removeStorageSync('ybHistory');
  },

  // input相关--onblur后面真机看是否保留
  onConfirm: function (e) {
    if (this.data.isHeightParams) return;
    let keyword = e.detail.value;
    const {isLogin} = this.data;
    if (keyword.trim()) {
      isLogin && this.addHistorySearch(keyword);
      isLogin &&
        addHistory({
          keyword,
          model_type: 'RESEARCH_REPORT_SEARCH'
        });
    }
  },
  addHistorySearch(value) {
    let historySearch = wx.getStorageSync('ybHistory') || [];
    let has = historySearch.includes(value);
    if (has) {
      let index = historySearch.findIndex(item => item == value);
      if (index == 0) return;
      historySearch.splice(index, 1);
    }
    let len = historySearch.length;
    if (len >= 10) {
      historySearch.pop();
    }
    historySearch.unshift(value);
    wx.setStorageSync('ybHistory', historySearch);
    this.setData({
      historyList: historySearch
    });
  },
  onClear() {
    this.unLocked();
    this.setData({
      report_name: ''
    });
    this.inputQuest();
  },
  init() {
    this.setData({
      report_name: '',
      inputShowed: false
    });
  },
  isBlur() {
    this.setData({
      inputShowed: true
    });
  },
  onBlur() {
    // 拿到report_name --传入最近搜索历史
    const report_name_value = this.data.report_name;
    if (report_name_value.trim().length > 0) {
      if (this.data.isHeightParams) return;
      this.addHistorySearch(report_name_value);
    } else {
      this.setData({
        inputShowed: false
      });
    }
  },
  onInput: debounce(function ([...e]) {
    const {isLogin} = this.data;
    let keyword = e[0].detail.value;
    if (keyword || keyword == '') {
      this.setData({
        report_name: keyword
      });

      if (keyword.trim() && isLogin) {
        this.addHistorySearch(keyword);
        addHistory({
          keyword,
          model_type: 'RESEARCH_REPORT_SEARCH'
        });
      }
      this.inputQuest();
    }
  }),
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      inputShowed: false
    });
  },
  inputQuest() {
    const {report_name: keyword} = this.data;
    if (!keyword) {
      this.setData({
        searchReportList: [],
        company_num: 0,
        searchRequestParams: {
          ...this.data.searchRequestParams,
          keyword: ''
        }
      });
      return;
    }
    this.setData({
      // 更新搜索参数，触发API请求
      searchRequestParams: {
        ...this.data.searchRequestParams,
        keyword: keyword
      }
    });
  },

  // 点击最近搜索
  historyTap(e) {
    const keyword = e.target.dataset['item'];
    this.setData(
      {
        inputShowed: true,
        report_name: keyword
      },
      () => this.inputQuest()
    );
  },
  // 点击删除图标
  handleIcon(e) {
    const type = e.currentTarget.dataset['index'];
    const that = this;
    switch (type) {
      // 清空最近搜索
      case 'a':
        wx.showModal({
          title: '删除搜索',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              wx.removeStorageSync('ybHistory');
              that.setData(
                {
                  historyList: []
                },
                () => {
                  that.scrollH();
                }
              );
              // 发请求同步
              clearHistory('RESEARCH_REPORT_SEARCH');
            }
          }
        });
        break;
      case 'b': //清空浏览历史
        wx.showModal({
          title: '删除浏览历史',
          content: '确定要删除浏览历史?',
          success: function (res) {
            if (res.confirm) {
              // 发送请求 --成功删除历史
              detBevHis('RESEARCH_REPORT_SEARCH').then(() => {
                app.showToast('删除成功!');
                that.setData({
                  browsingHistory: []
                });
              });
            }
          }
        });
        break;
      default:
        break;
    }
  },

  async getSearchHisList() {
    //获取搜索历史 列表
    let arr = [];
    getHistory('RESEARCH_REPORT_SEARCH')
      .then(res => {
        if (res && res.length > 0) {
          arr = res.map(item => item.key_word);
          arr = [...new Set(arr)];
        }
        wx.setStorageSync('ybHistory', arr);
        this.setData(
          {
            historyList: arr
          },
          () => {
            this.calculateAllHeights();
          }
        );
      })
      .catch(err => {
        console.log(err);
      });
  },

  async getBevHisList() {
    //获取浏览历史 列表
    try {
      const res = await getBevHis('RESEARCH_REPORT_SEARCH');
      if (res && res.length > 0) {
        // 转换浏览历史数据格式
        const historyList = res
          .map(item => {
            try {
              // 尝试解析enterprise_log中的额外信息
              const extraInfo = JSON.parse(item.extra_param);
              const tags = [];
              if (extraInfo.chains && Array.isArray(extraInfo.chains)) {
                // 取前2个产业链作为标签
                tags.push(
                  ...extraInfo.chains.slice(0, 2).map(chain => chain.name)
                );
              }
              return {
                id: extraInfo.id,
                title: extraInfo.report_name || '--',
                size: extraInfo.file_size || '-',
                tags: tags,
                organization: extraInfo.publish_org,
                date: this.formatDate(extraInfo.publish_time),
                pdfUrl: extraInfo.report_oss_url || '',
                imgTit:
                  extraInfo.report_type === 'REPORT_TYPE_1'
                    ? '撼地智库'
                    : '产业专题',
                page_num: extraInfo.page_num,
                // 保留原始数据以备后用
                originalData: extraInfo
              };
            } catch (error) {
              // 如果解析失败，使用基本信息
              console.log('解析浏览历史失败:', error);
            }
          })
          .slice(0, 10); // 限制显示10条

        this.setData({
          browsingHistory: historyList
        });
      } else {
        this.setData({
          browsingHistory: []
        });
      }
    } catch (error) {
      console.error('获取浏览历史失败:', error);
      this.setData({
        browsingHistory: []
      });
    }
  },

  // 一次性计算所有高度
  calculateAllHeights() {
    getHeight(this, ['.searchs', '.company_num', '.his_titles'], data => {
      const {screeHeight, res} = data;

      // 搜索框高度
      const searchHeight = res[0]?.height || 0;

      // 搜索结果标题高度
      const companyNumHeight = res[1]?.height || 60;

      // 历史记录标题高度和位置
      const hisTitleHeight = res[2]?.height || 0;
      const hisTitleTop = res[2]?.top || 0;

      // 计算搜索结果区域高度
      const searchScrollHeight = screeHeight - searchHeight - companyNumHeight; // 40为边距

      // 计算历史记录区域高度
      const scrollHeight = screeHeight - hisTitleHeight - hisTitleTop; // 20为边距

      this.setData({
        searchScrollHeight: Math.max(searchScrollHeight, 300),
        scrollHeight: Math.max(scrollHeight, 300),
        isHeightCalculated: true
      });
    });
  },
  // 搜索数据变化回调
  onSearchDataChange(e) {
    const {list, total} = e.detail;
    // 转换API数据格式为ReportCard组件期望的格式
    const transformedList = this.transformReportData(list);
    this.setData({
      searchReportList: transformedList,
      company_num: total
    });
  },

  // 转换API数据格式
  transformReportData(apiList) {
    if (!Array.isArray(apiList)) {
      return [];
    }

    return apiList.map(item => {
      // 处理产业链标签
      const tags = [];
      if (item.chains && Array.isArray(item.chains)) {
        // 取前2个产业链作为标签
        tags.push(...item.chains.slice(0, 2).map(chain => chain.name));
      }
      return {
        id: item.id,
        title: item.report_name || '--',
        size: item.file_size || '-',
        tags: tags,
        organization: item.publish_org,
        date: this.formatDate(item.publish_time),
        pdfUrl: item.report_oss_url || '',
        imgTit: item.report_type === 'REPORT_TYPE_1' ? '撼地智库' : '产业专题',
        page_num: item.page_num,
        // 保留原始数据以备后用
        originalData: item
      };
    });
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '未知日期';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      return dateString; // 如果格式化失败，返回原始字符串
    }
  },

  // 点击研报事件
  onReportClick(e) {
    const {item} = e.detail;
    // 显示操作选择弹窗
    this.showReportActionSheet(item);
  },

  // 添加研报到浏览历史
  addReportToHistory(reportItem) {
    try {
      // 发送浏览历史到服务器
      addBevHis({
        enterprise_name: reportItem.title,
        enterprise_id: reportItem.id, // 如果没有id，使用title
        model_type: 'RESEARCH_REPORT_SEARCH',
        extra_param: JSON.stringify(reportItem.originalData)
      });

      // 更新本地浏览历史
      this.updateLocalBrowsingHistory(reportItem);
    } catch (error) {
      console.error('添加浏览历史失败:', error);
    }
  },

  // 更新本地浏览历史
  updateLocalBrowsingHistory(reportItem) {
    let {browsingHistory} = this.data;

    // 检查是否已存在
    const existIndex = browsingHistory.findIndex(
      item => item.title === reportItem.title || item.id === reportItem.id
    );

    if (existIndex !== -1) {
      // 如果已存在，移到最前面
      browsingHistory.splice(existIndex, 1);
    }

    // 添加到最前面
    browsingHistory.unshift({
      ...reportItem,
      viewTime: new Date().toISOString()
    });

    // 限制历史记录数量
    if (browsingHistory.length > 10) {
      browsingHistory = browsingHistory.slice(0, 10);
    }

    this.setData({
      browsingHistory
    });
  },

  // 显示研报操作选择
  showReportActionSheet(reportItem) {
    const itemList = ['在线预览', '下载到本地'];
    const that = this;
    wx.showActionSheet({
      itemList,
      success: res => {
        switch (res.tapIndex) {
          case 0:
            this.previewReport(reportItem);
            // 添加到浏览历史
            that.addReportToHistory(reportItem);
            break;
          case 1:
            this.downloadReport(reportItem);
            // 添加到浏览历史
            that.addReportToHistory(reportItem);
            break;
        }
      },
      fail: res => {
        console.log('用户取消操作');
      }
    });
  },

  // 在线预览研报
  previewReport(reportItem) {
    wx.showLoading({
      title: '正在加载...'
    });

    // 获取PDF URL
    const pdfUrl =
      reportItem.pdfUrl ||
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    // 使用微信内置PDF预览
    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            success: () => {
              console.log('PDF预览成功');
            },
            fail: error => {
              console.error('PDF预览失败:', error);
              wx.showToast({
                title: '预览失败，请稍后重试',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载PDF失败:', error);
        wx.showToast({
          title: '加载失败，请检查网络',
          icon: 'none'
        });
      }
    });
  },

  // 下载研报到本地
  downloadReport(reportItem) {
    wx.showLoading({
      title: '正在下载...'
    });

    // 获取PDF URL
    const pdfUrl =
      reportItem.pdfUrl ||
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          // 保存到相册或文件管理器
          wx.saveFile({
            tempFilePath: res.tempFilePath,
            success: saveRes => {
              wx.showToast({
                title: '下载成功',
                icon: 'success'
              });
              console.log('文件保存路径:', saveRes.savedFilePath);
            },
            fail: error => {
              console.error('保存文件失败:', error);
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载失败:', error);
        wx.showToast({
          title: '下载失败，请稍后重试',
          icon: 'none'
        });
      }
    });
  }
});
