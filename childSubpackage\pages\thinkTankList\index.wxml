<import src="/template/null/null"></import>
<view class="pages">
  <!-- input -->
  <view class="searchs">
    <view class="s-input" bindtap="navigateToSearch">
      <view class="s-input-img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
          mode="aspectFit"
        ></image>
      </view>
      <view class="s-input-item">
        <input
          class="s-input-item-i"
          type="text"
          placeholder="请输入关键词"
          placeholder-class="placeholder"
          disabled="{{true}}"
        />
      </view>
    </view>
  </view>

  <!-- 列表页面 -->
  <view class="list-container">
    <view class="company_num"> </view>

    <!-- VIP用户：正常显示列表 -->
    <view
      wx:if="{{isVip}}"
      class="card-box"
      style="height: {{listScrollHeight}}px;"
    >
      <refresh-scroll
        id="listRefreshScroll"
        container-height="{{listScrollHeight}}"
        request-url="{{RequestUrlFn}}"
        requestParams="{{listRequestParams}}"
        empty-text="暂无研报数据"
        empty-tip=""
        bind:datachange="onListDataChange"
        custom-wrapper-class="custom-wrapper-class"
        requestType="pageIndex"
      >
        <view slot="content" class="list_wrp">
          <ReportCard
            report-list="{{reportList}}"
            bindreportclick="onReportClick"
          />
        </view>
      </refresh-scroll>
    </view>

    <!-- 非VIP用户：显示一条数据 + VIP页面 -->
    <scroll-view
      wx:else
      class="non-vip-container"
      scroll-y="{{true}}"
      style="height: {{listScrollHeight}}px;"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <!-- 显示第一条研报数据 -->
      <view wx:if="{{firstReportData}}" class="first-report-container">
        <SingleReportCard
          report-data="{{firstReportData}}"
          bindreportclick="onReportClick"
        />
      </view>

      <!-- VIP购买页面 -->
      <vip-page wx:if="{{showVipPage}}" bindpaySuccess="onVipPaySuccess">
        <!-- 可以在这里添加额外的提示内容 -->
      </vip-page>
    </scroll-view>
  </view>
</view>
