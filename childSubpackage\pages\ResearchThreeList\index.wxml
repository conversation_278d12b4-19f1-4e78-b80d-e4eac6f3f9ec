<import src="/template/null/null"></import>
<view class="pages">
  <!-- input -->
  <view class="searchs">
    <view class="s-input">
      <view class="s-input-img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
          mode="aspectFit"
        ></image>
      </view>
      <view class="s-input-item">
        <input
          class="s-input-item-i"
          type="text"
          placeholder="请输入机构/研报关键字"
          placeholder-class="placeholder"
          bindblur="onBlur"
          value="{{report_name}}"
          focus="{{inputShowed}}"
          bindinput="onInput"
          bindconfirm="onConfirm"
          confirm-type="search"
        />
        <view
          hidden="{{report_name.length <= 0}}"
          catchtap="onClear"
          class="input-clear"
        >
          <view class="clearIcon"></view>
        </view>
      </view>
    </view>
  </view>
  <!-- 历史记录 -->
  <view class="history_wrap" hidden="{{report_name}}">
    <!-- 最近搜索 -->
    <block wx:if="{{historyList.length>0}}">
      <view class="page__autofit search_a">
        <view class="his_title">
          <text class="his_title_l">最近搜索</text>
          <view class="his_title_icon" bindtap="handleIcon" data-index="a">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="his_content">
          <!-- 内容 -->
          <view class="text-box">
            <block wx:for="{{historyList}}" wx:key="index">
              <view
                class="his_content_item"
                bindtap="historyTap"
                data-item="{{item}}"
              >
                {{item}}
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>
    <!-- 浏览历史 - 保留原功能，但内容用研报展示 -->
    <view
      class="page__autofit search_b"
      hidden="{{!(browsingHistory.length>0)}}"
    >
      <view class="his_titles">
        <text class="his_title_l">浏览历史</text>
        <view class="his_title_icon" bindtap="handleIcon" data-index="b">
          <image
            src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png"
            mode="aspectFit"
          ></image>
        </view>
      </view>
      <scroll-view scroll-y style="height:{{scrollHeight}}px;">
        <view class="his_content1">
          <!-- 使用公共研报卡片组件，添加外层容器 -->
          <view class="report-list-wrap">
            <ReportCard
              report-list="{{browsingHistory}}"
              max-count="{{10}}"
              bindreportclick="onReportClick"
            />
          </view>
          <view style="height: 100rpx;"></view>
        </view>
      </scroll-view>
    </view>
  </view>
  <!-- 搜索结果列表 -->
  <view hidden="{{!report_name}}" class="search-results">
    <!-- 参考BusinessListComponent的company_num结构 -->
    <view class="company_num">
      <view class="text_left">
        共找到 <text class="color_num">{{company_num}}</text> 份报告
      </view>
    </view>

    <!-- 使用refresh-scroll组件实现下拉刷新上拉加载 -->
    <view class="card-box" style="height: {{searchScrollHeight}}px;">
      <refresh-scroll
        id="searchRefreshScroll"
        container-height="{{searchScrollHeight}}"
        request-url="{{RequestUrlFn}}"
        requestParams="{{searchRequestParams}}"
        empty-text="暂无研报数据"
        empty-tip=""
        bind:datachange="onSearchDataChange"
        custom-wrapper-class="custom-wrapper-class"
        requestType="pageIndex"
      >
        <view slot="content" class="list_wrp">
          <ReportCard
            report-list="{{searchReportList}}"
            bindreportclick="onReportClick"
          />
        </view>
      </refresh-scroll>
    </view>
  </view>
</view>
