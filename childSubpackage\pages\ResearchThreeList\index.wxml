<import src="/template/null/null"></import>
<view class="pages">
  <!-- input -->
  <view class="searchs">
    <view class="s-input">
      <view class="s-input-img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
          mode="aspectFit"
        ></image>
      </view>
      <view class="s-input-item">
        <input
          class="s-input-item-i"
          type="text"
          placeholder="请输入机构/研报关键字"
          placeholder-class="placeholder"
          bindblur="onBlur"
          value="{{report_name}}"
          focus="{{inputShowed}}"
          bindinput="onInput"
          bindconfirm="onConfirm"
          confirm-type="search"
        />
        <view
          hidden="{{report_name.length <= 0}}"
          catchtap="onClear"
          class="input-clear"
        >
          <view class="clearIcon"></view>
        </view>
      </view>
    </view>
  </view>
  <!-- 历史记录 -->
  <view class="history_wrap" hidden="{{report_name}}">
    <!-- 最近搜索 -->
    <block wx:if="{{historyList.length>0}}">
      <view class="page__autofit search_a">
        <view class="his_title">
          <text class="his_title_l">最近搜索</text>
          <view class="his_title_icon" bindtap="handleIcon" data-index="a">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="his_content">
          <!-- 内容 -->
          <view class="text-box">
            <block wx:for="{{historyList}}" wx:key="index">
              <view
                class="his_content_item"
                bindtap="historyTap"
                data-item="{{item}}"
              >
                {{item}}
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>

    <!-- AI图片 -->
    <image
      class="ai_img"
      src="/image/report/r_ai_enter.png"
      bindtap="handleClick"
      data-type="ai"
    />

    <!-- 浏览历史 -->
    <view
      class="page__autofit search_b"
      hidden="{{!(browsingHistory.length>0)}}"
    >
      <view class="his_titles">
        <text class="his_title_l">最新报告</text>
      </view>
      <scroll-view scroll-y="true" style="height:{{scrollHeight - 200}}px;">
        <view class="his_content1">
          <!-- 内容 -->
          <view
            class="his_content1_item"
            wx:for="{{browsingHistory}}"
            wx:key="index"
            bindtap="goDetail"
            data-item="{{item}}"
          >
            <text class="his_content1_item-l">{{item.enterprise_name}}</text>
            <text class="his_content1_item-r">{{item.create_time}}</text>
          </view>
          <!-- 占位 -->
          <view class="his_content1_item"> </view>
          <view class="his_content1_item"> </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 搜索结果内容 -->
  <view class="search-content" hidden="{{!report_name}}">
    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 左侧 TabBar -->
      <view class="left-tabbar">
        <view
          class="tab-item {{currentTab === 'report' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="report"
        >
          <text class="tab-text">报告</text>
        </view>
        <view
          class="tab-item {{currentTab === 'chart' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="chart"
        >
          <text class="tab-text">图表</text>
        </view>
        <view
          class="tab-item {{currentTab === 'library' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="library"
        >
          <text class="tab-text">撼地智库</text>
        </view>
      </view>

      <!-- 右侧图片占位 -->
      <view class="right-image">
        <image
          class="placeholder-img"
          src="/image/placeholder.png"
          mode="aspectFit"
        />
        <text class="placeholder-text">图片占位</text>
      </view>
    </view>

    <!-- 搜索结果列表 -->
    <view class="search-results">
      <!-- 撼地智库特殊列表 -->
      <view wx:if="{{currentTab === 'library'}}" class="library-list">
        <text class="result-count"
          >共找到 {{libraryReportList.length}} 条研报</text
        >

        <!-- 使用撼地智库列表组件 -->
        <LibraryReportList
          requestParams="{{libraryRequestParams}}"
          containerHeight="{{listScrollHeight}}"
          bind:datachange="onLibraryDataChange"
          bind:error="onLibraryError"
          bind:reportclick="onLibraryReportClick"
        />
      </view>

      <!-- 其他类型的普通列表 -->
      <view wx:else class="normal-list">
        <text class="result-count"
          >共找到 {{currentResults.length}} 条结果</text
        >

        <view
          class="result-item"
          wx:for="{{currentResults}}"
          wx:key="id"
          bindtap="onResultClick"
          data-item="{{item}}"
        >
          <text class="result-title">{{item.title}}</text>
          <text class="result-desc">{{item.description}}</text>
          <view class="result-type">{{item.type}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
