import {getHeight} from '../../../utils/height';
import {getReportPageListApi, addBevHis} from '../../../service/industryApi';
import {hasPrivile} from '../../../utils/route';

const app = getApp();

Page({
  data: {
    isLogin: app.isLogin(),
    // VIP相关
    isVip: false,
    showVipPage: false,
    // 研报数据
    reportList: [],
    firstReportData: null, // 第一条研报数据，用于非VIP展示
    listRequestParams: {
      type: 'chainMap' // 热门研报
    },
    RequestUrlFn: getReportPageListApi,
    // 列表数据
    company_num: 0,
    listScrollHeight: 600, // 列表滚动高度，设置默认值
    // 高度计算相关
    isHeightCalculated: false // 是否已计算过高度
  },
  onShow() {
    const {login} = app.globalData;
    this.setData({
      isLogin: login
    });
    this.calculateAllHeights();
    // 组件会自动处理VIP状态检查
  },

  onLoad(options) {
    this.setData({
      pageOptions: options
    });
  },

  onReady() {
    // 页面渲染完成后一次性计算所有高度
    this.calculateAllHeights();
  },

  // 检查VIP状态
  async checkVipStatus() {
    console.log('开始检查VIP状态...');
    // 临时测试：强制设置为非VIP用户，显示VIP页面
    this.setData({
      isVip: false,
      showVipPage: true
    });
    console.log('设置 isVip: false, showVipPage: true');

    await this.getFirstReportData();
    console.log('checkVipStatus 完成');
    return;
    if (!this.data.isLogin) {
      this.setData({
        isVip: false,
        showVipPage: true
      });
      // 未登录用户也获取第一条数据用于展示
      await this.getFirstReportData();
      return;
    }

    try {
      const vipStatus = await hasPrivile({
        packageType: true
      });

      const isVip = vipStatus !== '游客' && vipStatus !== '普通VIP';

      this.setData({
        isVip,
        showVipPage: !isVip
      });

      // 如果是非VIP用户，获取第一条数据
      if (!isVip) {
        await this.getFirstReportData();
      }
    } catch (error) {
      console.error('检查VIP状态失败:', error);
      this.setData({
        isVip: false,
        showVipPage: true
      });
      // 获取第一条数据
      await this.getFirstReportData();
    }
  },

  // 获取第一条研报数据（用于非VIP用户展示）
  async getFirstReportData() {
    console.log('开始获取第一条研报数据...');
    try {
      const response = await getReportPageListApi({
        type: 'chainMap',
        page_index: 1,
        page_size: 1
      });

      console.log('API响应:', response);

      if (response && response.list && response.list.length > 0) {
        const transformedList = this.transformReportData(response.list);
        console.log('转换后的数据:', transformedList[0]);
        this.setData({
          firstReportData: transformedList[0],
          company_num: response.total || 0
        });
        console.log('设置 firstReportData 成功');
      } else {
        console.log('API返回数据为空，使用mock数据');
        // 使用mock数据
        const mockData = {
          id: 'mock_1',
          title: '2024年新能源汽车产业深度分析报告',
          size: '3.2MB',
          tags: ['新能源汽车', '产业链'],
          organization: '撼地研究院',
          date: '2024-01-15',
          pdfUrl: '',
          imgTit: '撼地智库',
          page_num: 68,
          originalData: {
            id: 'mock_1',
            report_name: '2024年新能源汽车产业深度分析报告',
            file_size: '3.2MB',
            publish_org: '撼地研究院',
            publish_time: '2024-01-15',
            report_type: 'REPORT_TYPE_1',
            page_num: 68
          }
        };
        this.setData({
          firstReportData: mockData,
          company_num: 1
        });
      }
    } catch (error) {
      console.error('获取第一条研报数据失败:', error);
      // API失败时使用mock数据
      const mockData = {
        id: 'mock_1',
        title: '2024年新能源汽车产业深度分析报告',
        size: '3.2MB',
        tags: ['新能源汽车', '产业链'],
        organization: '撼地研究院',
        date: '2024-01-15',
        pdfUrl: '',
        imgTit: '撼地智库',
        page_num: 68,
        originalData: {
          id: 'mock_1',
          report_name: '2024年新能源汽车产业深度分析报告',
          file_size: '3.2MB',
          publish_org: '撼地研究院',
          publish_time: '2024-01-15',
          report_type: 'REPORT_TYPE_1',
          page_num: 68
        }
      };
      this.setData({
        firstReportData: mockData,
        company_num: 1
      });
    }
  },

  // 点击搜索框跳转到搜索页面
  navigateToSearch() {
    // 跳转到ResearchThreeList页面，并传递type=hdzk和from=thinkTankList参数
    const url =
      '/childSubpackage/pages/ResearchThreeList/index?type=hdzk&from=thinkTankList';
    wx.navigateTo({
      url: url
    });
  },

  // 一次性计算所有高度
  calculateAllHeights() {
    getHeight(this, ['.company_num'], data => {
      const {screeHeight, res} = data;

      // 搜索框高度
      const barH = res[0]?.top || 0;
      // 计算列表区域高度
      const listScrollHeight = screeHeight - barH;
      this.setData({
        listScrollHeight: listScrollHeight,
        isHeightCalculated: true
      });
    });
  },

  // 列表数据变化回调
  onListDataChange(e) {
    const {list, total} = e.detail;
    // 转换API数据格式为ReportCard组件期望的格式
    const transformedList = this.transformReportData(list);

    // 根据VIP状态处理数据
    let displayList = transformedList;
    let firstReportData = null;

    if (!this.data.isVip && transformedList.length > 0) {
      // 非VIP用户只显示第一条数据
      firstReportData = transformedList[0];
      displayList = [];
    }

    this.setData({
      reportList: displayList,
      firstReportData: firstReportData,
      company_num: total
    });
  },

  // 转换API数据格式
  transformReportData(apiList) {
    if (!Array.isArray(apiList)) {
      return [];
    }

    return apiList.map(item => {
      // 处理产业链标签
      const tags = [];
      if (item.chains && Array.isArray(item.chains)) {
        // 取前2个产业链作为标签
        tags.push(...item.chains.slice(0, 2).map(chain => chain.name));
      }
      return {
        id: item.id,
        title: item.report_name || '--',
        size: item.file_size || '-',
        tags: tags,
        organization: item.publish_org,
        date: this.formatDate(item.publish_time),
        pdfUrl: item.report_oss_url || '',
        imgTit: item.report_type === 'REPORT_TYPE_1' ? '撼地智库' : '产业专题',
        page_num: item.page_num,
        // 保留原始数据以备后用
        originalData: item
      };
    });
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '未知日期';
    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      return dateString; // 如果格式化失败，返回原始字符串
    }
  },

  // VIP支付成功回调
  onVipPaySuccess() {
    // 重新检查VIP状态
    this.checkVipStatus().then(() => {
      // 如果成为VIP，刷新数据
      if (this.data.isVip) {
        // 触发refresh-scroll重新加载数据
        const refreshScroll = this.selectComponent('#listRefreshScroll');
        if (refreshScroll) {
          refreshScroll.refresh();
        }
      }
    });
  },

  // 点击研报事件
  onReportClick(e) {
    const {item} = e.detail;
    const isvip = this.data.isVip;
    if (!isvip) {
      app.showToast('请购买VIP后查看', 'none', 2000, false);
      return;
    }

    // 显示操作选择弹窗
    this.showReportActionSheet(item);
  },

  // 添加研报到浏览历史
  addReportToHistory(reportItem) {
    try {
      // 发送浏览历史到服务器
      addBevHis({
        enterprise_name: reportItem.title,
        enterprise_id: reportItem.id, // 如果没有id，使用title
        model_type: 'RESEARCH_REPORT_SEARCH',
        extra_param: JSON.stringify(reportItem.originalData)
      });
    } catch (error) {
      console.error('添加浏览历史失败:', error);
    }
  },

  // 显示研报操作选择
  showReportActionSheet(reportItem) {
    const itemList = ['在线预览', '下载到本地'];
    const that = this;
    wx.showActionSheet({
      itemList,
      success: res => {
        switch (res.tapIndex) {
          case 0:
            this.previewReport(reportItem);
            // 添加到浏览历史 用不上
            // that.addReportToHistory(reportItem);
            break;
          case 1:
            this.downloadReport(reportItem);
            // 添加到浏览历史----------用不上
            // that.addReportToHistory(reportItem);
            break;
        }
      },
      fail: () => {
        console.log('用户取消操作');
      }
    });
  },

  // 在线预览研报
  previewReport(reportItem) {
    wx.showLoading({
      title: '正在加载...'
    });

    // 获取PDF URL
    const pdfUrl =
      reportItem.pdfUrl ||
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    // 使用微信内置PDF预览
    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            success: () => {
              console.log('PDF预览成功');
            },
            fail: error => {
              console.error('PDF预览失败:', error);
              wx.showToast({
                title: '预览失败，请稍后重试',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载PDF失败:', error);
        wx.showToast({
          title: '加载失败，请检查网络',
          icon: 'none'
        });
      }
    });
  },

  // 下载研报到本地
  downloadReport(reportItem) {
    wx.showLoading({
      title: '正在下载...'
    });

    // 获取PDF URL
    const pdfUrl =
      reportItem.pdfUrl ||
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

    wx.downloadFile({
      url: pdfUrl,
      success: res => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          // 保存到相册或文件管理器
          wx.saveFile({
            tempFilePath: res.tempFilePath,
            success: saveRes => {
              wx.showToast({
                title: '下载成功',
                icon: 'success'
              });
              console.log('文件保存路径:', saveRes.savedFilePath);
            },
            fail: error => {
              console.error('保存文件失败:', error);
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        }
      },
      fail: error => {
        wx.hideLoading();
        console.error('下载失败:', error);
        wx.showToast({
          title: '下载失败，请稍后重试',
          icon: 'none'
        });
      }
    });
  }
});
